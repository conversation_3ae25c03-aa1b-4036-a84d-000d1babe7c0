import 'package:and/model/voice_recognize.dart';
import 'package:and/module/chat/widget/message/position/message_position_renderer.dart';
import 'package:and/widget/popup_window.dart';
import 'package:and/widget/reminder_animation.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class MessageItemCallback {
  Function(WKMsg msg) resendMessage;
  Function(WKMsg msg) reEditMessage;
  VoiceRecognize? Function(WKMsg msg)? getVoiceRecognize;
  Function(WKMsg msg, GlobalKey key)? pickEmoji;
  Function(WKMsg msg, String emoji)? emojiReaction;
  Function(String channelId)? viewUserInfo;
  bool Function(WKMsg msg)? isShowMessageTime;
  Function(WKMsg msg)? toggleShowMessageTime;
  bool Function(WKMsg msg)? isManagerMsg;
  bool isAnonymous;

  MessageItemCallback(
      {required this.resendMessage,
      required this.reEditMessage,
      this.getVoiceRecognize,
      this.pickEmoji,
      this.emojiReaction,
      this.viewUserInfo,
      this.isShowMessageTime,
      this.toggleShowMessageTime,
      this.isManagerMsg,
      this.isAnonymous = false});
}

class UiMsgItem extends StatefulWidget {
  final WKMsg msg;
  final bool isSelectedMode;
  final bool isSelected;
  final Function()? onMessageViewed;
  final Function(GlobalKey popKey, LongPressStartDetails)? onLongPressTap;
  final Function(bool)? onSelectedChange;
  final MessageItemCallback messageItemCallback;
  final Function()? onTap;
  final bool isHighlighted;

  const UiMsgItem({
    super.key,
    required this.msg,
    this.isSelectedMode = false,
    this.isSelected = false,
    this.onMessageViewed,
    this.onLongPressTap,
    this.onSelectedChange,
    required this.messageItemCallback,
    this.onTap,
    this.isHighlighted = false,
  });

  @override
  State<StatefulWidget> createState() {
    return _UiChannelItemState();
  }
}

class _UiChannelItemState extends State<UiMsgItem> {
  GlobalKey popupKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    var isSelectedMode = widget.isSelectedMode;
    return VisibilityDetector(
      key: Key(widget.msg.clientMsgNO),
      onVisibilityChanged: (visibilityInfo) {
        var visiblePercentage = visibilityInfo.visibleFraction * 100;

        if (visiblePercentage >= 80) {
          widget.onMessageViewed?.call();
        }
      },
      child: GestureDetector(
        key: popupKey,
        behavior: isSelectedMode ? HitTestBehavior.deferToChild : null,
        onLongPressStart: (details) {
          widget.onLongPressTap?.call(popupKey, details);
        },
        onTap: isSelectedMode
            ? () {
                widget.onSelectedChange?.call(!widget.isSelected);
              }
            : widget.onTap,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.isSelectedMode)
              Container(
                width: 40,
                height: 40,
                alignment: Alignment.center,
                child: Checkbox(
                  value: widget.isSelected,
                  onChanged: (value) {
                    widget.onSelectedChange?.call(value ?? false);
                  },
                ),
              ),
            Expanded(child: _buildContent()),
          ],
        ),
      ),
    );
  }

  @override
  void didUpdateWidget(covariant UiMsgItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.msg != oldWidget.msg && mounted) {
      setState(() {});
    }
  }

  Widget _buildContent() {
    return Stack(
      children: [
        // 当消息需要高亮时显示闪烁动画
        if (widget.isHighlighted)
          Positioned.fill(
            child: ReminderAnimationWidget(
              count: 3,
              height: null, // 设置为null使其自适应高度
            ),
          )
        else
          Container(),
        Padding(
          padding: EdgeInsets.all(10),
          child: MessagePositionRendererRegistry()
                  .getRenderer(widget.msg)
                  ?.render(context, widget.msg, widget.messageItemCallback) ??
              Container(),
        )
      ],
    );
  }
}
