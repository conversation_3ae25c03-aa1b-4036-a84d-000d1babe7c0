import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/module/chat/widget/message/type/message_content_renderer.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import '../../../../../app.dart';

class ReplyContentWidget extends StatefulWidget {
  final WKMsg msg;

  const ReplyContentWidget({super.key, required this.msg});

  @override
  State<StatefulWidget> createState() {
    return _ReplyContentWidgetState();
  }
}

class _ReplyContentWidgetState extends State<ReplyContentWidget> {
  WKReply? get reply => widget.msg.messageContent?.reply;
  final GlobalKey _replyKey = GlobalKey();

  late Future<String> _replyName;

  @override
  void initState() {
    super.initState();
    _replyName = _getReplyName();
  }

  @override
  void didUpdateWidget(ReplyContentWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _replyName = _getReplyName();
  }

  Future<String> _getReplyName() async {
    String channelDisplay = '';
    if (reply?.fromUID.isNotEmpty ?? false) {
      var fromUID = reply?.fromUID ?? '';
      String memberDisplay = '';
      int channelType = widget.msg.channelType;
      String channelID = widget.msg.channelID;
      if (fromUID == CacheHelper.uid) {
        return globalContext?.l10n.you ?? '';
      }
      if (channelType == WKChannelType.group) {
        var memberFrom = await WKIM.shared.channelMemberManager
            .getMember(channelID, channelType, fromUID);
        memberDisplay = memberFrom?.displayName ?? '';
        if (memberDisplay.isNotEmpty) {
          return memberDisplay;
        }
        var channelInfo =
            await WKIM.shared.channelManager.getChannel(channelID, channelType);
        var isAnonymous = channelInfo?.anonymous == 1;
        //如果是匿名群
        if (isAnonymous) {
          channelDisplay = globalContext?.l10n.anonymous ?? '';
        } else {
          var channelFrom = await WKIM.shared.channelManager
              .getChannel(fromUID, WKChannelType.personal);
          channelDisplay = channelFrom?.displayName ?? '';
        }
      } else {
        var channelFrom = await WKIM.shared.channelManager
            .getChannel(fromUID, WKChannelType.personal);
        channelDisplay = channelFrom?.displayName ?? '';
      }
    }
    return channelDisplay;
  }

  Widget _buildReplyName(BuildContext context) {
    return FutureBuilder<String?>(
      future: _replyName,
      builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
        var name = snapshot.data ?? '';
        if (name.isNotEmpty) {
          name += ": ";
        }
        return Text(
          name,
          style: TextStyles.fontSize13Normal.copyWith(color: DColor.hintText),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (reply?.payload == null) {
      return Container();
    }

    return GestureDetector(
      behavior: HitTestBehavior.deferToChild,
      onTap: () async {
        _pressTap();
      },
      child: Container(
          margin: EdgeInsets.only(top: 4),
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          decoration: BoxDecoration(
            color: DColor.greyE7,
            borderRadius: BorderRadius.circular(8),
          ),
          child: RichText(
            textScaler: MediaQuery.of(context).textScaler,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            text: TextSpan(style: TextStyles.fontSize15Normal, children: [
              WidgetSpan(child: _buildReplyName(context)),
              WidgetSpan(
                  alignment: PlaceholderAlignment.top,
                  child: Padding(
                    padding: EdgeInsets.only(left: 5),
                    child: _buildContent(context),
                  )),
            ]),
          )),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (reply == null) {
      return Container();
    }
    WKMsg replyMsg = WKMsg();
    replyMsg.contentType = reply?.payload?.contentType ?? 0;

    var wkMsgExtra = WKMsgExtra();
    wkMsgExtra.revoke = reply?.revoke ?? 0;
    replyMsg.wkMsgExtra ??= wkMsgExtra;
    return Container(
      key: _replyKey,
      child: MessageRendererRegistry()
              .getRenderer(replyMsg)
              ?.reply(context, widget.msg) ??
          Container(),
    );
  }

  void _pressTap() {
    List<GestureDetector> gestureDetectors =
        findGestureDetectors<GestureDetector>();
    if (gestureDetectors.isNotEmpty) {
      gestureDetectors.first.onTap?.call();
      return;
    }
    List<InkWell> inkWellDetectors = findGestureDetectors<InkWell>();
    if (inkWellDetectors.isNotEmpty) {
      inkWellDetectors.first.onTap?.call();
      return;
    }
  }

  // 遍历子组件的方法
  List<T> findGestureDetectors<T>() {
    List<T> detectors = [];

    // 获取父组件的 Element
    final BuildContext? context = _replyKey.currentContext;
    if (context == null) return detectors;

    // 递归遍历子 Element
    void visitElement(Element element) {
      // 检查当前 Element 对应的 Widget 是否为 GestureDetector
      if (element.widget is T) {
        detectors.add(element.widget as T);
      }
      // 递归遍历子 Element
      element.visitChildren(visitElement);
    }

    // 从父 Element 开始遍历
    visitElement(context as Element);
    return detectors;
  }
}
