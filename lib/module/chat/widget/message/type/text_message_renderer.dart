import 'package:and/common/res/text_styles.dart';
import 'package:and/module/chat/widget/message/common/msg_status_widget.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:custom_text/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_message_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'message_content_renderer.dart';

class TextMessageRenderer extends MessageContentRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    return TextContentWidget(
      channelID: msg.channelID,
      channelType: msg.channelType,
      messageContent: msg.messageContent,
      extraWidget: Padding(
        padding: EdgeInsets.only(left: 5),
        child: MsgStatusWidget(
            msg: msg,
            onResendMsgTap: () {
              callback.resendMessage(msg);
            }),
      ),
    );
  }

  @override
  bool canRender(WKMsg msg) {
    return msg.contentType == WkMessageContentType.text;
  }
}

class TextContentWidget extends StatefulWidget {
  final int channelType;
  final String channelID;
  final WKMessageContent? messageContent;
  final Widget? extraWidget;
  final double? textSize;

  const TextContentWidget(
      {super.key,
      required this.channelType,
      required this.channelID,
      required this.messageContent,
      this.textSize,
      this.extraWidget});

  @override
  State<StatefulWidget> createState() {
    return _TextContentWidgetState();
  }
}

class _TextContentWidgetState extends State<TextContentWidget> {
  late Future<List<WKChannelMember>> _membersFuture;

  @override
  void initState() {
    super.initState();
    _membersFuture = getMentionMembers();
  }

  @override
  void didUpdateWidget(covariant TextContentWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _membersFuture = getMentionMembers();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<WKChannelMember>>(
      future: _membersFuture,
      builder: (BuildContext context,
          AsyncSnapshot<List<WKChannelMember>> snapshot) {
        var content = widget.messageContent?.content ?? '';

        return CustomText.spans(spans: [
          TextSpan(
            text: content.trim(),
            style: TextStyles.fontSize15Normal.copyWith(
              fontSize: widget.textSize ?? 15,
            ),
          ),
          if (widget.extraWidget != null)
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: widget.extraWidget!,
            ),
        ], definitions: [
          ..._buildMemberDefinitions(snapshot.data ?? []),
          ..._buildCommonDefinitions(),
        ]);
      },
    );
  }

  List<Definition> _buildMemberDefinitions(List<WKChannelMember> members) {
    return members.map((member) {
      // 创建正则表达式，匹配以 @ 开头的成员名称
      var namePattern = RegExp.escape(member.memberName);
      var remarkPattern = member.memberRemark.isNotEmpty
          ? RegExp.escape(member.memberRemark)
          : '';
      var pattern = '@$namePattern';
      if (remarkPattern.isNotEmpty) {
        pattern += '|$remarkPattern';
      }

      return TextDefinition(
        matcher: PatternMatcher(pattern),
        matchStyle: TextStyle(
          color: Colors.blue,
          decoration: TextDecoration.underline,
          decorationColor: Colors.blue,
        ),
        onTap: (detail) {
          UserInfoPage.open(channelID: member.memberUID);
        },
      );
    }).toList();
  }

  List<Definition> _buildCommonDefinitions() {
    return [
      TextDefinition(
        matcher: const UrlMatcher(),
        matchStyle: const TextStyle(
          color: Colors.blue,
          decoration: TextDecoration.underline,
          decorationColor: Colors.blue,
        ),
        tapStyle: const TextStyle(color: Colors.indigo),
        onTap: (detail) {
          CommonHelper.launchInAppBrowser(detail.actionText);
        },
      ),
      TextDefinition(
        matcher: const EmailMatcher(),
        matchStyle: TextStyle(
          color: Colors.blue,
          decoration: TextDecoration.underline,
          decorationColor: Colors.blue,
        ),
        onTap: (detail) {
          CommonHelper.launchEmail(detail.actionText);
        },
      ),
      TextDefinition(
        matcher: const TelMatcher(r'(?:\+?[1-9]\d{0,4})?(?:[- ]?\d{1,4}){7,}'),
        matchStyle: TextStyle(
          color: Colors.blue,
          decoration: TextDecoration.underline,
          decorationColor: Colors.blue,
        ),
        onTap: (detail) {
          CommonHelper.launchPhone(detail.actionText);
        },
      )
    ];
  }

  Future<List<WKChannelMember>> getMentionMembers() async {
    //高亮mention文本
    var mentionInfo = widget.messageContent?.mentionInfo;
    var members = <WKChannelMember>[];
    for (var uid in mentionInfo?.uids ?? []) {
      var member = await WKIM.shared.channelMemberManager
          .getMember(widget.channelID, widget.channelType, uid);
      if (member != null) {
        members.add(member);
      }
    }

    return members;
  }
}
