import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/widget/message/type/call_message_renderer.dart';
import 'package:and/module/chat/widget/message/type/card_message_renderer.dart';
import 'package:and/module/chat/widget/message/type/video_message_renderer.dart';
import 'package:and/module/chat/widget/message/type/file_message_renderer.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

import 'image_message_renderer.dart';
import 'multi_forward_message_renderer.dart';
import 'revoked_message_renderer.dart';
import 'system_message_renderer.dart';
import 'text_message_renderer.dart';
import 'unknown_message_renderer.dart';
import 'voice_message_renderer.dart';

abstract class MessageContentRenderer {
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback);

  Widget reply(BuildContext context, WKMsg msg) {
    var reply = msg.messageContent?.reply;
    if (reply == null) {
      return Container();
    }
    var content = reply.payload?.content;
    if (content?.isEmpty ?? true) {
      content = reply.payload?.displayText() ?? context.l10n.unknowMsgType;
    }
    return GestureDetector(
      onTap: () {
        Get.to(SafeArea(
            child: Center(
          child: SingleChildScrollView(
            child: TextContentWidget(
              channelID: msg.channelID,
              channelType: msg.channelType,
              messageContent: reply.payload,
              textSize: 35,
            ),
          ),
        )));
      },
      child: Text(content ?? context.l10n.unknowMsgType,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: TextStyles.fontSize13Normal.copyWith(color: DColor.hintText)),
    );
  }

  bool canRender(WKMsg msg);

  bool needContainer() {
    return false;
  }
}

class MessageRendererRegistry {
  static final MessageRendererRegistry _instance =
      MessageRendererRegistry._internal();

  factory MessageRendererRegistry() => _instance;

  MessageRendererRegistry._internal();

  final List<MessageContentRenderer> _renderers = [
    RevokedMessageRenderer(),
    SystemMessageRenderer(),
    TextMessageRenderer(),
    ImageMessageRenderer(),
    VideoMessageRenderer(),
    FileMessageRenderer(),
    VoiceMessageRenderer(),
    CardMessageRenderer(),
    MultiForwardMessageRenderer(),
    CallMessageRenderer(),
    UnknownMessageRenderer(),
  ];

  void registerRenderer(int type, MessageContentRenderer renderer) {
    _renderers[type] = renderer;
  }

  MessageContentRenderer? getRenderer(WKMsg msg) {
    for (var renderer in _renderers) {
      if (renderer.canRender(msg)) {
        return renderer;
      }
    }
    return null;
  }
}
